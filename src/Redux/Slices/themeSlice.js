import { createSlice } from '@reduxjs/toolkit';
import AsyncStorage from '@react-native-async-storage/async-storage';

const initialState = {
  themeColor: '#4a00ff',
  selectedTheme: 'light',
  backgroundColor: '#ffffff',
  language: 'en', // Default language is English
};

const themeSlice = createSlice({
  name: 'theme',
  initialState,
  reducers: {
    setThemeColor: (state, action) => {
      state.themeColor = action.payload;
      AsyncStorage.setItem('themeColor', action.payload);
    },
    setSelectedTheme: (state, action) => {
      state.selectedTheme = action.payload;
    },
    setBackgroundColor: (state, action) => {
      state.backgroundColor = action.payload;
      AsyncStorage.setItem('backgroundColor', action.payload);
    },
    loadThemeColor: (state, action) => {
      state.themeColor = action.payload;
    },
    loadBackgroundColor: (state, action) => {
      state.backgroundColor = action.payload;
    },
    setLanguage: (state, action) => {
      state.language = action.payload;
      AsyncStorage.setItem('language', action.payload);
    },
    loadLanguage: (state, action) => {
      state.language = action.payload;
    },
  },
});

export const {
  setThemeColor,
  setSelectedTheme,
  setBackgroundColor,
  loadThemeColor,
  loadBackgroundColor,
  setLanguage,
  loadLanguage
} = themeSlice.actions;

export default themeSlice.reducer;