// useTheme.js
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { colors } from '../Custom/Colors';
import { loadBackgroundColor, loadThemeColor, loadLanguage } from './Slices/themeSlice';

const useTheme = () => {
  const dispatch = useDispatch();
  const themeColor = useSelector((state) => state.theme.themeColor);
  const backgroundColor = useSelector((state) => state.theme.backgroundColor);
  const selectedTheme = useSelector((state) => state.theme.selectedTheme);
  const language = useSelector((state) => state.theme.language);

  // Function to determine if the theme is dark
  const isDarkTheme = () => {
    const darkThemes = ['dark', 'synthwave', 'forest', 'water', 'night', 'coffee', 'dim', 'sunset', 'business', 'halloween', 'black', 'luxury', 'dracula'];
    return darkThemes.includes(selectedTheme);
  };

  // Get text color based on theme
  const getTextColor = () => {
    return isDarkTheme() ? colors.white : colors.greyBlack;
  };

  // Get secondary text color based on theme
  const getSecondaryTextColor = () => {
    return isDarkTheme() ? colors.white : colors.greyborder;
  };

  useEffect(() => {
    const loadTheme = async () => {
      try {
        const savedColor = await AsyncStorage.getItem('themeColor');
        const savedBackgroundColor = await AsyncStorage.getItem('backgroundColor');
        const savedLanguage = await AsyncStorage.getItem('language');
        if (savedColor) {
          dispatch(loadThemeColor(savedColor));
        }
        if (savedBackgroundColor) {
          dispatch(loadBackgroundColor(savedBackgroundColor));
        }
        if (savedLanguage) {
          dispatch(loadLanguage(savedLanguage));
        }
      } catch (error) {
        console.error('Error loading theme:', error);
      }
    };
    loadTheme();
  }, [dispatch]);

  return {
    themeColor,
    backgroundColor,
    selectedTheme,
    language,
    isDarkTheme,
    getTextColor,
    getSecondaryTextColor,
  };
};

export default useTheme;