import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  Text,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import * as Animatable from 'react-native-animatable';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import CustomHeader from '../Components/CustomHeader';
import {colors} from '../Custom/Colors';
import {hp, wp} from '../Custom/Responsiveness';
import ResponsiveText from '../Custom/RnText';
import Icon from '../Custom/Icon';
import {globalpath} from '../Custom/globalpath';
import CustomDropDown from '../Components/CustomDropDown';
import FileUploadCard from '../Components/FileUploadCard';
import {useNavigation} from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {ImmigrantsAPI} from '../api/services/immigrants';
import useTheme from '../Redux/useTheme';
import {ServiceApi} from '../api/services/services';
import AdminDropDown from '../Components/AdminDropDown';
import moment from 'moment';
import PhoneInput from 'react-native-phone-number-input';
import {CommonActions} from '@react-navigation/native';
import OfficeDropDown from '../Components/OfficeDropDown';
import AddOfficeDropDown from '../Components/AddOfficeDropDown';
import Add_member_Dropdown from '../Components/Add_member_Dropdown';
import CalendarModal from '../Components/CalendarModal';
import Calendar_Drop_Down from '../Components/Calendar_Drop_Down';
import FcCalendar from '../Components/FcCalendar';

const CategoryCard = ({title, icon, isExpanded, onPress, children}) => {
  const {themeColor, backgroundColor, getTextColor, getSecondaryTextColor} =
    useTheme();

  return (
    <View style={styles.categoryContainer}>
      <TouchableOpacity
        style={[
          styles.categoryCard,
          isExpanded && [
            styles.categoryCardActive,
            {backgroundColor: themeColor},
          ],
        ]}
        onPress={onPress}>
        <View style={styles.categoryHeader}>
          <Icon
            source={icon}
            size={wp(5)}
            tintColor={isExpanded ? colors.white : colors.greyBlack}
            // tintColor={'white'}
          />
          <ResponsiveText
            //  style={[styles.categoryTitle, isExpanded && styles.categoryTitleActive]}
            color={isExpanded ? colors.white : colors.greyBlack}
            weight={'600'}
            size={4}>
            {title}
          </ResponsiveText>
        </View>
        <Icon
          source={globalpath.up}
          size={wp(3)}
          tintColor={isExpanded ? colors.white : colors.greyBlack}
          style={{transform: [{rotate: isExpanded ? '180deg' : '0deg'}]}}
        />
      </TouchableOpacity>
      {isExpanded && (
        <Animatable.View
          animation="fadeIn"
          duration={300}
          style={styles.expandedContent}>
          {children}
        </Animatable.View>
      )}
    </View>
  );
};

const ServiceCard = ({onBack, onSave, editData, service}) => {
  const [user, setUser] = useState(null);
  console.log('service--', service);
  const {themeColor, backgroundColor, getTextColor, getSecondaryTextColor} =
    useTheme();
  const [selectedAdmin, setSelectedAdmin] = useState(
    editData?.operator?.id || '',
  );
  const [savedPayments, setSavedPayments] = useState(
    editData?.financialRecords?.map(record => ({
      advancePayment: record.amount.toString(),
      paymentMethod: record.paymentMethod,
      nextDepositDate: new Date(record.date).toISOString().split('T')[0],
    })) || [],
  );
  const [showPaymentForm, setShowPaymentForm] = useState(true);
  const [editingPaymentIndex, setEditingPaymentIndex] = useState(null);
  useEffect(() => {
    setFormData(prev => ({
      ...prev,
      operator: selectedAdmin,
    }));
  }, [selectedAdmin]);

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const userData = await AsyncStorage.getItem('user');
        if (userData) {
          const parsedUser = JSON.parse(userData);
          console.log('User data:', parsedUser.name);
          setUser(parsedUser);
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
      }
    };

    fetchUserData();
  }, []);
  const [formData, setFormData] = useState({
    practiceEstimate: editData ? editData.amount.toString() : '',
    entrustPractice: '',
    hasBalancePayment: false,
    advancePayment: '',
    paymentMethod: '',
    nextDepositDate: null,
    operator: selectedAdmin,
    applicationDeadline: editData?.applicationDeadline
      ? new Date(editData.applicationDeadline).toISOString().split('T')[0]
      : null,
    use: editData?.description || '',
  });

  const [isDatePickerVisible, setDatePickerVisible] = useState({
    deposit: false,
    deadline: false,
  });

  const paymentMethodOptions = [
    {label: 'Cash', value: 'cash'},
    {label: 'Card', value: 'card'},
    {label: 'Wire Transfer', value: 'wire'},
  ];
  const OnLineConsultancypaymentMethodOptions = [
    {label: 'Card', value: 'card'},
    {label: 'Wire Transfer', value: 'wire'},
  ];

  const handleBalancePaymentToggle = () => {
    setFormData(prev => ({
      ...prev,
      hasBalancePayment: !prev.hasBalancePayment,
    }));
  };

  const handleSavePayment = () => {
    const newPaymentAmount = Number(formData.advancePayment);
    const practiceEstimate = Number(formData.practiceEstimate);

    if (isNaN(newPaymentAmount) || newPaymentAmount <= 0) {
      Alert.alert(
        'Invalid Amount',
        'Please enter a valid advance payment amount.',
      );
      return;
    }

    // Calculate total of existing payments (excluding the one being edited if applicable)
    const existingTotal = savedPayments.reduce((sum, payment, index) => {
      if (editingPaymentIndex !== null && index === editingPaymentIndex) {
        return sum; // skip the current one being edited
      }
      return sum + Number(payment.advancePayment || 0);
    }, 0);

    const newTotal = existingTotal + newPaymentAmount;

    if (newTotal > practiceEstimate) {
      Alert.alert(
        'Exceeded Estimate',
        `The total advance payments (${newTotal}) exceed the practice estimate (${practiceEstimate}).`,
      );
      return;
    }

    const newPayment = {
      advancePayment: newPaymentAmount,
      paymentMethod: formData.paymentMethod,
      nextDepositDate: formData.nextDepositDate,
    };

    console.log('newPayment==', formData);

    setSavedPayments(prevPayments => {
      const updatedPayments =
        editingPaymentIndex !== null
          ? prevPayments.map((item, index) =>
              index === editingPaymentIndex ? newPayment : item,
            )
          : [...prevPayments, newPayment];

      console.log('Updated Payments==:', updatedPayments);
      return updatedPayments;
    });

    setFormData(prev => ({
      ...prev,
      advancePayment: '',
      paymentMethod: '',
      nextDepositDate: null,
    }));
    setShowPaymentForm(false);
    setEditingPaymentIndex(null);
  };

  const handleEditPayment = index => {
    const paymentToEdit = savedPayments[index];
    setFormData(prev => ({
      ...prev,
      advancePayment: paymentToEdit.advancePayment,
      paymentMethod: paymentToEdit.paymentMethod,
      nextDepositDate: paymentToEdit.nextDepositDate,
    }));
    setEditingPaymentIndex(index);
    setShowPaymentForm(true);
  };

  const handleDeletePayment = index => {
    const updatedPayments = savedPayments.filter((_, i) => i !== index);
    setSavedPayments(updatedPayments);
    console.log('Updated Payments after deletion:', updatedPayments);
  };

  const renderSavedPayments = () => {
    return savedPayments.map((payment, index) => (
      <Animatable.View
        animation="fadeIn"
        style={[styles.savedPaymentCard]}
        key={index}>
        <View style={styles.savedPaymentContent}>
          <View style={styles.savedPaymentInfo}>
            <View style={styles.paymentHeader}>
              <ResponsiveText weight={'700'} size={4}>
                Payment {index + 1}
              </ResponsiveText>
              <View style={styles.savedPaymentActions}>
                <TouchableOpacity
                  onPress={() => handleEditPayment(index)}
                  style={[styles.iconButton, {backgroundColor: themeColor}]}>
                  <Icon
                    source={globalpath.edit}
                    size={wp(4)}
                    tintColor={colors.white}
                  />
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => handleDeletePayment(index)}
                  style={[styles.iconButton, {backgroundColor: colors.red}]}>
                  <Icon
                    source={globalpath.del}
                    size={wp(4)}
                    tintColor={colors.white}
                  />
                </TouchableOpacity>
              </View>
            </View>
            <View style={styles.paymentDetails}>
              <View style={styles.detailRow}>
                <Icon
                  source={globalpath.payments}
                  size={wp(4)}
                  tintColor={themeColor}
                />

                <ResponsiveText size={3.8} margin={[0, 0, 0, wp(2)]}>
                  Amount: €{' '}
                  {new Intl.NumberFormat('it-IT', {
                    minimumFractionDigits: 2,
                  }).format(payment?.advancePayment || 0)}
                </ResponsiveText>
              </View>
              <View style={styles.detailRow}>
                <Icon
                  source={globalpath.wallet}
                  size={wp(4)}
                  tintColor={themeColor}
                />
                <ResponsiveText size={3.8} margin={[0, 0, 0, wp(2)]}>
                  Method: {payment.paymentMethod}
                </ResponsiveText>
              </View>
              <View style={styles.detailRow}>
                <Icon
                  source={globalpath.calendar}
                  size={wp(4)}
                  tintColor={themeColor}
                />
                <ResponsiveText size={3.8} margin={[0, 0, 0, wp(2)]}>
                {`Next Deposit: ${moment(payment.nextDepositDate).format('M/D/YYYY')}`}
                </ResponsiveText>
              </View>
            </View>
          </View>
        </View>
      </Animatable.View>
    ));
  };

  const renderPaymentSection = () => (
    <View>
      <ResponsiveText weight={'700'} size={4.2} margin={[0, 0, wp(3), 0]}>
        Payments:
      </ResponsiveText>
      {renderSavedPayments()}

      {showPaymentForm && (
        <Animatable.View animation="fadeIn" style={styles.paymentFormCard}>
          <ResponsiveText style={styles.cardTitle}>
            {editingPaymentIndex !== null ? 'Edit Payment' : 'New Payment'}
          </ResponsiveText>
          <View style={styles.inputContainer}>
            <ResponsiveText style={styles.inputLabel}>
              Advance Payment
            </ResponsiveText>

            <View style={{position: 'relative'}}>
              <TextInput
                style={[styles.input2, {color: 'transparent'}]} // hide real input
                keyboardType="numeric"
                value={formData.advancePayment?.toString() || ''}
                onChangeText={text => {
                  const cleaned = text.replace(/[^\d]/g, ''); // keep only digits
                  setFormData(prev => ({
                    ...prev,
                    advancePayment: Number(cleaned),
                  }));
                }}
                placeholder="€0.00"
              />

              {/* Display formatted overlay */}
              <View style={{position: 'absolute', left: wp(4), top: hp(1.5)}}>
                <ResponsiveText size={4}>
                  {formData.advancePayment
                    ? `€ ${new Intl.NumberFormat('it-IT', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      }).format(formData.advancePayment)}`
                    : ''}
                </ResponsiveText>
              </View>
            </View>
          </View>

          <CustomDropDown
            data={
              service.label !== 'CONSULENZA ONLINE'
                ? paymentMethodOptions
                : OnLineConsultancypaymentMethodOptions
            }
            value={formData.paymentMethod}
            onChange={item =>
              setFormData(prev => ({...prev, paymentMethod: item.value}))
            }
            placeholder="Select payment method"
          />
          {service.label !== 'CONSULENZA ONLINE' &&
            service.label !== 'CONSULENZA' &&
            !formData.hasBalancePayment && (
              <TouchableOpacity
                style={styles.dateInput}
                onPress={() =>
                  setDatePickerVisible(prev => ({...prev, deposit: true}))
                }>
                <ResponsiveText style={styles.inputLabel}>
                  Next Deposit Date
                </ResponsiveText>
                <View style={styles.dateInputContainer}>
                  <TextInput
                    style={styles.dateTextInput}
                    editable={false}
                    value={formData.nextDepositDate}
                    placeholder="Select date"
                  />
                  <Icon
                    source={globalpath.calendar}
                    size={wp(5)}
                    color={themeColor}
                  />
                </View>
              </TouchableOpacity>
            )}
          <View
            style={[
              styles.buttonRow,
              !formData.hasBalancePayment ? {} : {justifyContent: 'flex-end'},
            ]}>
            {!formData.hasBalancePayment && (
              <TouchableOpacity
                style={[styles.button, styles.backButton]}
                onPress={() => {
                  setShowPaymentForm(false);
                  setEditingPaymentIndex(null);
                  setFormData(prev => ({
                    ...prev,
                    advancePayment: '',
                    paymentMethod: '',
                    nextDepositDate: null,
                  }));
                }}>
                <ResponsiveText color={colors.greyBlack} weight={'700'}>
                  Back
                </ResponsiveText>
              </TouchableOpacity>
            )}

            <TouchableOpacity
              style={[
                styles.button,
                {
                  backgroundColor:
                    formData.advancePayment && formData.paymentMethod
                      ? themeColor
                      : colors.lightGrey,
                },
                formData.hasBalancePayment ? {flex: 0.5} : {},
              ]}
              disabled={!formData.advancePayment || !formData.paymentMethod}
              onPress={handleSavePayment}>
              <ResponsiveText
                color={
                  formData.advancePayment && formData.paymentMethod
                    ? colors.white
                    : colors.greyBlack
                }
                weight={'600'}>
                {editingPaymentIndex !== null ? 'Update' : 'Add'}
              </ResponsiveText>
            </TouchableOpacity>
          </View>
        </Animatable.View>
      )}

      {!formData.hasBalancePayment && !showPaymentForm && (
        <TouchableOpacity
          style={[styles.addPaymentButton, {backgroundColor: themeColor}]}
          onPress={() => setShowPaymentForm(true)}>
          <Icon
            source={globalpath.plus}
            size={wp(5)}
            tintColor={colors.white}
          />
          <ResponsiveText
            color={colors.white}
            weight={'700'}
            margin={[0, 0, 0, wp(2)]}>
            Add Payment
          </ResponsiveText>
        </TouchableOpacity>
      )}
    </View>
  );

  const handleSaveService = () => {
    console.log('serviceeeeeeeee---', formData);
console.log('saved payments', savedPayments);
    const totalAdvancePayment = savedPayments.reduce((sum, payment) => {
      const amount = parseFloat(payment.advancePayment);
      return sum + (isNaN(amount) ? 0 : amount);
    }, 0);

    if (totalAdvancePayment > formData.practiceEstimate) {
      Alert.alert(
        'Warning',
        'The total advance payment cannot exceed the practice estimate.',
      );
      return; // stop the save process
    }
    const updatedFormData = {
      ...formData,
      nextDepositDate: savedPayments[0]?.nextDepositDate || null,
    };
    const serviceData = {
      ...updatedFormData,
      savedPayments: savedPayments,
      id: editData?.id || null,
    };

    console.log('servicedaaaaaaaatttaa', updatedFormData);
    onSave(serviceData);
  };

  // Check if form is valid based on required fields
  const isFormValid = () => {
    const hasRequiredFields = formData.practiceEstimate && selectedAdmin;
    if (showPaymentForm) {
      // If payment form is shown, require at least one payment
      return hasRequiredFields && savedPayments.length > 0;
    }
    // If payment form is not shown, only require practice estimate and admin
    return hasRequiredFields;
  };

  return (
    <View style={styles.serviceCardContainer}>
      <View style={styles.inputContainer}>
        <ResponsiveText weight={'700'} margin={[0, 0, wp(3), 0]}>
          Practice Estimate:{' '}
          <ResponsiveText color={colors.red}>*</ResponsiveText>
        </ResponsiveText>
        <View style={{position: 'relative'}}>
          <TextInput
            style={[
              styles.input2,
              !formData.practiceEstimate && styles.inputRequired,
              {color: 'transparent'}, // hide real input text
            ]}
            keyboardType="numeric"
            value={formData.practiceEstimate?.toString() || ''}
            onChangeText={text => {
              const cleaned = text.replace(/[^\d]/g, ''); // only digits
              setFormData(prev => ({
                ...prev,
                practiceEstimate: Number(cleaned),
              }));
            }}
            placeholder="€0.00"
          />

          {/* Overlay formatted text */}
          <View style={{position: 'absolute', left: wp(4), top: hp(1.5)}}>
            <ResponsiveText size={4}>
              {formData.practiceEstimate
                ? `€ ${new Intl.NumberFormat('it-IT', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  }).format(formData.practiceEstimate)}`
                : ''}
            </ResponsiveText>
          </View>
        </View>
      </View>

      <View style={styles.adminSection}>
        <ResponsiveText weight={'700'} margin={[0, 0, wp(3), 0]}>
          Select Admin: <ResponsiveText color={colors.red}>*</ResponsiveText>
        </ResponsiveText>
        <Calendar_Drop_Down value={selectedAdmin} setValue={setSelectedAdmin} />
      </View>

      {/* <ResponsiveText weight={'700'}>Balance Payment?</ResponsiveText>
      <View style={styles.checkboxContainer}>
        <ResponsiveText style={styles.checkboxLabel}>
          Balance Payment
        </ResponsiveText>
        <TouchableOpacity
          style={[styles.checkbox, {borderColor: themeColor}]}
          onPress={handleBalancePaymentToggle}>
          <View
            style={[
              styles.checkboxInner,
              formData.hasBalancePayment && [
                styles.checkboxChecked,
                {backgroundColor: themeColor},
              ],
            ]}
          />
        </TouchableOpacity>
      </View> */}
      {service.label !== 'CONSULENZA' && renderPaymentSection()}

      {service.label !== 'CONSULENZA ONLINE' &&
        service.label !== 'CONSULENZA' && (
          <TouchableOpacity
            style={styles.dateInput}
            onPress={() =>
              setDatePickerVisible(prev => ({...prev, deadline: true}))
            }>
            <ResponsiveText style={styles.inputLabel}>
              Application Deadline
            </ResponsiveText>
            <View style={[styles.dateInputContainer, {marginTop: hp(1)}]}>
              <TextInput
                style={styles.dateTextInput}
                editable={false}
                value={formData.applicationDeadline}
                placeholder="Select date"
              />
              <Icon
                source={globalpath.calendar}
                size={wp(5)}
                color={themeColor}
              />
            </View>
          </TouchableOpacity>
        )}

      {/* <View style={styles.inputContainer}>
        <ResponsiveText style={styles.inputLabel}>Notes</ResponsiveText>
        <TextInput
          style={styles.input2}
          value={formData.use}
          onChangeText={text => setFormData(prev => ({...prev, use: text}))}
          placeholder="Enter Notes"
        />
      </View> */}

      <View style={styles.buttonRow}>
        <TouchableOpacity
          style={[styles.button, styles.backButton]}
          onPress={onBack}>
          <ResponsiveText style={styles.buttonText}>Back</ResponsiveText>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.button,
            styles.saveButton,
            {backgroundColor: themeColor},
            !isFormValid() &&
              service.label !== 'CONSULENZA' &&
              styles.buttonDisabled,
          ]}
          disabled={service.label === 'CONSULENZA' ? false : !isFormValid()}
          onPress={handleSaveService}>
          <ResponsiveText
            color={
              service.label === 'CONSULENZA'
                ? colors.white
                : !isFormValid()
                ? colors.greyBlack
                : colors.white
            }
            weight={'600'}>
            Save Service
          </ResponsiveText>
        </TouchableOpacity>
      </View>

      <DateTimePickerModal
        isVisible={isDatePickerVisible.deposit}
        mode="date"
        onConfirm={date => {
          setFormData(prev => ({
            ...prev,
            nextDepositDate: date.toLocaleDateString('en-CA'),
          }));
          setDatePickerVisible(prev => ({...prev, deposit: false}));
        }}
        onCancel={() =>
          setDatePickerVisible(prev => ({...prev, deposit: false}))
        }
      />

      <DateTimePickerModal
        isVisible={isDatePickerVisible.deadline}
        mode="date"
        onConfirm={date => {
          setFormData(prev => ({
            ...prev,
            applicationDeadline: date.toLocaleDateString('en-CA'),
          }));
          setDatePickerVisible(prev => ({...prev, deadline: false}));
        }}
        onCancel={() =>
          setDatePickerVisible(prev => ({...prev, deadline: false}))
        }
      />
    </View>
  );
};

const Add_Customer = () => {
  const {themeColor, backgroundColor, getTextColor, getSecondaryTextColor} =
    useTheme();

  //states
  const navigation = useNavigation();
  const [expandedCategory, setExpandedCategory] = useState(null);
  const [formData, setFormData] = useState({
    date: moment(new Date()).format('YYYY-MM-DD'),

    name: '',
    surname: '',
    telephone: '',
    use: '',
    reservationAt: '',
    reservationEndAt: '',
  });
  const [isDatePickerVisible, setDatePickerVisible] = useState(false);
  const [selectedService, setSelectedService] = useState(null);
  const [showServiceCard, setShowServiceCard] = useState(false);
  const [savedServices, setSavedServices] = useState([]);
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(false);
  const [services, setServices] = useState([]);
  const [uploadedImages, setUploadedImages] = useState([]);
  const [countryCode, setCountryCode] = useState('IT'); // ISO code for defaultCode
  const [dialCode, setDialCode] = useState('+39'); // Actual dialing code
  const [office, setOffice] = useState(null); //for theme:
  const [startDateModal, setStartDateModal] = useState(false);
  const [endDateModal, setEndDateModal] = useState(false);
  const [startTimeModal, setStartTimeModal] = useState(false);
  const [endTimeModal, setEndTimeModal] = useState(false);
  const [selectedAdmin, setSelectedAdmin] = useState(null);
  const [selectedStartDate, setSelectedStartDate] = useState(null); // for Start Date
  const [selectedStartTime, setSelectedStartTime] = useState(null); // for Start Time
  const [selectedEndDate, setSelectedEndDate] = useState(null); // for End Date
  const [selectedEndTime, setSelectedEndTime] = useState(null);
  const [calendarModalVisible, setCalendarModalVisible] = useState(false);
  const [reservationData, setReservationData] = useState({
    selectedStartDate: null,
    selectedEndDate: null,
    selectedStartTime: null,
    selectedEndTime: null,
    reservationAt: null,
    reservationEndAt: null,
  });

  const phoneInputRef = useRef(null);
  const validatePhoneNumber = () => {
    const trimmedPhone = formData.telephone.trim();

    let isValid = false;

    if (phoneInputRef.current) {
      isValid = phoneInputRef.current?.isValidNumber(trimmedPhone);
    } else {
      // Fallback basic check — you can improve this
      isValid = /^\d{9,15}$/.test(trimmedPhone);
    }

    if (!isValid) {
      Alert.alert('Please enter a valid phone number.');
    }

    return isValid;
  };
  useEffect(() => {
    fetchServices();
  }, []);
  
  const handleFilesUploaded = newFiles => {
    setUploadedImages(prev => [...prev, ...newFiles]);
  };
  const fetchServices = async () => {
    console.log('Fetching services');
    try {
      const response = await ServiceApi.getServices({
        search: '',
      });
      setServices(response?.data?.reasons);
      console.log('services----', response.data.reasons);
      // Handle response
    } catch (error) {
      // Handle error
    }
  };
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const userData = await AsyncStorage.getItem('user');
        if (userData) {
          const parsedUser = JSON.parse(userData);
          console.log('User data:', parsedUser.name);
          setUser(parsedUser);
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
      }
    };

    fetchUserData();
  }, []);

  const handleReservationSave = data => {
    console.log('Data received from modal:', data.selectedTime, data.endTime);
    setSelectedStartDate(data.selectedTime);
    setSelectedEndDate(data.endTime);
    setSelectedAdmin(data.selectedValue);
    setFormData(prev => ({
      ...prev,
      reservationAt: data.selectedTime,
      reservationEndAt: data.endTime,
    }));
    // setSelectedStartDate(data.selectedStartDate);
    // setSelectedEndDate(data.selectedEndDate);
    // setSelectedStartTime(data.selectedStartTime);
    // setSelectedEndTime(data.selectedEndTime);
    // setSelectedAdmin(data.selectedValue);
    // setFormData(prev => ({
    //   ...prev,
    //   reservationEndAt: data.reservationEndAt,
    //   reservationAt: data.reservationAt,
    // }));
    // setReservationData(data);
  };

  const handleSave = async () => {
    console.log('FormData:=====', formData);
    console.log('savedServices:=====', savedServices);
    if (formData.name === '') {
      Alert.alert('Please Enter Name');
      return;
    }
    if (formData.surname === '') {
      Alert.alert('Please Enter SurName');
      return;
    }
    if (formData.telephone === '') {
      Alert.alert('Please Enter Phone No');
      return;
    }
    if (!validatePhoneNumber()) {
      return; // ❌ stop if phone number is invalid
    }

    // if (formData.use === '') {
    //   Alert.alert('Please Enter Notes');
    //   return;
    // }
    const fullPhoneNumber = `${dialCode}${formData.telephone.replace(
      /\s/g,
      '',
    )}`;
    const data = {
      adminId: user?.id,
      date: formData.date || new Date().toISOString(),
      description: formData.use,
      files: uploadedImages.map(doc => ({
        createdAt: doc.createdAt,
        id: doc.id,
        mimeType: doc.mimeType,
        name: doc.name || doc,
        size: doc.size || doc.size,
        url: doc.url,
      })),
      firstName: formData.name,
      lastName: formData.surname,
      officeCode: office?.referenceNumber,
      notes: [],
      ...(user?.role === 'firstContact' && {operator: selectedAdmin}),

      phoneNumber: fullPhoneNumber,
      practiceNumber: '',
      reservationAt:
        formData.reservationAt === '' ? null : formData.reservationAt,
      reservationEndAt:
        formData.reservationEndAt === '' ? null : formData.reservationEndAt,
      services: savedServices.map(service => ({
        amount: parseInt(service.amount, 10),
        description: service.description || '',

        financialRecords: service.financialRecords.map(record => ({
          amount: record.amount,
          createdAt: null,
          date: record.date,
          description: record.description,
          id: null,
          paymentMethod: record.paymentMethod,
        })),
        id: null,
        nextPaymentDate: service.nextPaymentDate,
        numPratica: '',
        operator: service.operator,
        reason: service.reason,
        reservations: [],
      })),
    };

    // Log the formatted data to console
    console.log('Formatted Customer Data:', JSON.stringify(data, null, 2));

    setLoading(true);
    try {
      const userData = await AsyncStorage.getItem('user');
      console.log('Formatted Customer Data', data);
      // Here you would typically make your API call to save the data
      const response = await ImmigrantsAPI.AddImmigrant(data);
      console.log('response', response);
      if (response?.data?.message === 'Creato con successo') {
        Alert.alert('Success', 'Customer data has been formatted successfully');

        if (userData) {
          const parsedUser = JSON.parse(userData);
          console.log('User datarole:', parsedUser);
          console.log('Before the navigation is -->');
          navigation.replace('CustomerDetails', {
            customerData: response?.data?.immigrant,
            role: parsedUser.role,
          });
        }
        // navigation.goBack();
      }
    } catch (error) {
      console.error(
        'HASSAN Error saving customer:',
        error?.response?.data?.message,
      );
      Alert.alert(
        'Attention',
        error?.response?.data?.error?.message || error?.response?.data?.message,
      );
    } finally {
      setLoading(false);
    }
  };

  const handleDateConfirm = date => {
    // Convert the Date object to an ISO string
    const formattedDate = moment(date).format('YYYY-MM-DD');

    console.log('ISO Date:', formattedDate);

    // Update state with the ISO string
    setFormData(prev => ({
      ...prev,
      date: formattedDate,
    }));

    setDatePickerVisible(false);
  };

  const renderInput = (label, value, key, isDate = false) => (
    <View style={styles.inputContainer}>
      <ResponsiveText style={styles.inputLabel}>{label}</ResponsiveText>
      <TouchableOpacity
        style={styles.input}
        onPress={() => isDate && setDatePickerVisible(true)}
        disabled={!isDate}>
        <TextInput
          style={styles.textInput}
          value={value}
          onChangeText={text =>
            setFormData(prev => ({
              ...prev,
              [key]: text.charAt(0).toUpperCase() + text.slice(1),
            }))
          }
          editable={!isDate}
          placeholder={isDate ? 'Select date' : `Enter ${label.toLowerCase()}`}
          placeholderTextColor={colors.darkGrey}
          keyboardType={key === 'telephone' ? 'phone-pad' : 'default'}
          autoCapitalize="sentences"
        />
        {isDate && (
          <Icon source={globalpath.calendar} size={wp(5)} color={themeColor} />
        )}
      </TouchableOpacity>
    </View>
  );

  const SavedServiceCard = ({service, onEdit, onDelete}) => {
    console.log('service-->>', service);
    const {themeColor} = useTheme();

    const formatDate = dateString => {
      try {
        const date = new Date(dateString);
        return date.toLocaleDateString();
      } catch (error) {
        return dateString;
      }
    };

    return (
      <Animatable.View animation="fadeIn" style={[styles.savedServiceCard]}>
        <View style={[styles.serviceCardHeader, {backgroundColor: themeColor}]}>
          <View style={styles.headerContent}>
            <Icon
              source={globalpath.services}
              size={wp(6)}
              tintColor={colors.white}
            />
            <View>
              <ResponsiveText
                weight={'700'}
                size={4}
                color={colors.white}
                margin={[0, 0, 0, wp(3)]}
                numberOfLines={1}>
                {service.reason.label}
              </ResponsiveText>
              <ResponsiveText
                weight={'700'}
                size={4}
                color={colors.white}
                margin={[0, 0, 0, wp(3)]}
                numberOfLines={1}>
                {`${service.reason.code} ${service.reason.startFrom}`}
              </ResponsiveText>
            </View>
          </View>
          <View style={styles.actionButtons}>
            <TouchableOpacity
              onPress={onEdit}
              style={[styles.headerIconButton]}>
              <Icon
                source={globalpath.edit}
                size={wp(4.5)}
                tintColor={colors.white}
                margin={[0, 0, 0, 0]}
              />
            </TouchableOpacity>
            <TouchableOpacity
              onPress={onDelete}
              style={[styles.headerIconButton]}>
              <Icon
                source={globalpath.del}
                size={wp(4.5)}
                tintColor={colors.white}
              />
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.serviceCardBody}>
          <View style={styles.infoSection}>
            <View style={styles.infoRow}>
              <Icon
                source={globalpath.payments}
                size={wp(5)}
                tintColor={themeColor}
              />

              <ResponsiveText size={3.8} margin={[0, 0, 0, wp(2)]}>
                Amount: €{' '}
                {new Intl.NumberFormat('it-IT', {
                  minimumFractionDigits: 2,
                }).format(service.amount || 0)}
              </ResponsiveText>
            </View>
            <View style={styles.infoRow}>
              <Icon
                source={globalpath.calendar}
                size={wp(5)}
                tintColor={themeColor}
              />
              <ResponsiveText size={3.8} margin={[0, 0, 0, wp(2)]}>
                Next Payment: {formatDate(service.financialRecords[0].date)}
              </ResponsiveText>
            </View>
            {service.description && (
              <View style={styles.infoRow}>
                <Icon
                  source={globalpath.document}
                  size={wp(5)}
                  tintColor={themeColor}
                />
                <ResponsiveText size={3.8} margin={[0, 0, 0, wp(2)]}>
                  Description: {service.description}
                </ResponsiveText>
              </View>
            )}
          </View>

          {service.financialRecords.length > 0 && (
            <View style={styles.paymentsContainer}>
              <View style={styles.paymentsSectionHeader}>
                <Icon
                  source={globalpath.payments}
                  size={wp(5)}
                  tintColor={themeColor}
                />
                <ResponsiveText
                  weight={'700'}
                  size={4}
                  margin={[0, 0, 0, wp(2)]}>
                  Payment History
                </ResponsiveText>
              </View>
              <View style={styles.paymentsList}>
                {service.financialRecords.map((payment, index) => (
                  <View key={index} style={styles.paymentRecord}>
                    <View style={styles.paymentAmount}>
                      <ResponsiveText
                        weight={'600'}
                        size={3.8}
                        color={themeColor}>
                        <ResponsiveText size={3.8} margin={[0, 0, 0, wp(2)]}>
                          Amount: €{' '}
                          {new Intl.NumberFormat('it-IT', {
                            minimumFractionDigits: 2,
                          }).format(payment.amount || 0)}
                        </ResponsiveText>
                        {/* €{payment.amount} */}
                      </ResponsiveText>
                      <ResponsiveText size={3.5} color={colors.darkGrey}>
                        {payment.paymentMethod}
                      </ResponsiveText>
                    </View>
                    <ResponsiveText size={3.5} color={colors.darkGrey}>
                      {formatDate(payment.date)}
                    </ResponsiveText>
                  </View>
                ))}
              </View>
            </View>
          )}
        </View>
      </Animatable.View>
    );
  };

  const handleServiceSave = serviceData => {
    console.log('service dataaaaaaa======', serviceData);
    if (!serviceData || !serviceData.savedPayments) {
      console.log('No service data or payments provided');
      return;
    }

    const newService = {
      id: serviceData.id,
      description: serviceData.use || '',
      reason: {
        id: selectedService.id,
        label: selectedService.label,
        maximumFinancialRecords: selectedService.maximumFinancialRecords,
        maximumReservations: selectedService.maximumReservations,
        code: selectedService.code,
        startFrom: selectedService.startFrom,
      },
      amount: serviceData.practiceEstimate,
      nextPaymentDate:  serviceData.applicationDeadline ? new Date(serviceData.applicationDeadline).toISOString(): new Date().toISOString(),
   
      numPratica: '',
      operator: {
        email: serviceData?.operator.email,
        id: serviceData?.operator.id,
        name: serviceData?.operator.name,
        role: serviceData?.operator.role,
      },
      financialRecords: serviceData.savedPayments.map(payment => ({
        id: null,
        date: payment.nextDepositDate
          ? new Date(payment.nextDepositDate).toISOString()
          : new Date().toISOString(),
        amount: parseFloat(payment.advancePayment),
        paymentMethod: payment.paymentMethod,
        description: null,
        createdAt: null,
      })),
      reservations: [],
    };
console.warn('newService====', newService);
    if (serviceData.id) {
      // If editing, update the existing service
      setSavedServices(prev =>
        prev.map(service =>
          service.id === serviceData.id ? newService : service,
        ),
      );
    } else {
      // If adding new, append to the list
      setSavedServices(prev => [...prev, newService]);
    }

    setShowServiceCard(false);
    setSelectedService(null);
  };

  const handleDeleteService = index => {
    setSavedServices(prev => prev.filter((_, i) => i !== index));
  };

  const handleEditService = index => {
    const serviceToEdit = savedServices[index];
    // Add an id to the service if it doesn't have one
    if (!serviceToEdit.id) {
      serviceToEdit.id = Date.now(); // Use timestamp as temporary id
    }
    setSelectedService(serviceToEdit.reason.label);
    setShowServiceCard(true);
    setExpandedCategory('services'); // Ensure services section is expanded
  };
  const handleSelectedService = item => {
    console.log('selectedservice======', item);
    setSelectedService(item);
  };

  return (
    <SafeAreaView
      style={[styles.container, {backgroundColor: backgroundColor}]}>
      <CustomHeader title="Add Customer" showBack />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{flex: 1}}>
        <ScrollView
          style={styles.content}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{paddingBottom: wp(13)}}
          keyboardShouldPersistTaps="handled">
          <View>
            <TouchableOpacity onPress={() => navigation.goBack()}>
              <Icon
                source={globalpath.back}
                size={wp(7)}
                margin={[0, 0, wp(4), 0]}
                tintColor={getTextColor()}
              />
            </TouchableOpacity>
          </View>
          <CategoryCard
            title="General"
            icon={globalpath.user}
            isExpanded={expandedCategory === 'general'}
            onPress={() =>
              setExpandedCategory(
                expandedCategory === 'general' ? null : 'general',
              )
            }>
            {renderInput('Date', formData.date, 'date', true)}
            {renderInput('Name', formData.name, 'name')}
            {renderInput('Surname', formData.surname, 'surname')}
            {/* {renderInput('Telephone', formData.telephone, 'telephone')} */}
            <PhoneInput
              ref={phoneInputRef}
              value={formData.telephone}
              defaultCode={countryCode}
              layout="first"
              onChangeText={text => {
                setFormData(prev => ({...prev, telephone: text}));
              }}
              onChangeFormattedText={formattedText => {
                console.log('Full Number with Code:', formattedText);
              }}
              onChangeCountry={country => {
                setCountryCode(country.cca2); // 'IT'
                setDialCode(`+${country.callingCode[0]}`); // '+39'
              }}
              withDarkTheme
              withShadow
              autoFocus
              containerStyle={styles.phoneInputContainer}
              textContainerStyle={styles.phoneInputTextContainer}
              textInputStyle={styles.phoneInputText}
              codeTextStyle={styles.phoneInputCodeText}
              flagButtonStyle={styles.phoneInputFlagButton}
            />

            <View>
              <ResponsiveText>Office</ResponsiveText>
              <AddOfficeDropDown value={office} setValue={setOffice} />
            </View>

            {user?.role === 'firstContact' && (
              <>
                <View style={{flex: 1}}>
                  <TouchableOpacity
                    onPress={() => setCalendarModalVisible(true)}
                    style={{
                      backgroundColor: themeColor,
                      padding: 12,
                      borderRadius: 8,
                      margin: 20,
                    }}>
                    <Text style={{color: 'white', textAlign: 'center'}}>
                      Open Calendar
                    </Text>
                  </TouchableOpacity>

                  <FcCalendar
                    visible={calendarModalVisible}
                    onClose={() => setCalendarModalVisible(false)}
                    onSave={handleReservationSave}
                  />
                </View>
              </>
            )}

            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
                marginTop: hp(2),
              }}>
              <ResponsiveText>Created By</ResponsiveText>
              <ResponsiveText>{user?.name}</ResponsiveText>
            </View>
          </CategoryCard>

          <CategoryCard
            title="Services"
            icon={globalpath.services}
            isExpanded={expandedCategory === 'services'}
            onPress={() =>
              setExpandedCategory(
                expandedCategory === 'services' ? null : 'services',
              )
            }>
            <View>
              <CustomDropDown
                data={services}
                value={selectedService}
                onChange={item => handleSelectedService(item)}
                placeholder="Select service"
                labelField="label"
                valueField="label"
              />

              <TouchableOpacity
                style={[
                  [styles.addServiceButton, {backgroundColor: themeColor}],
                  !selectedService && styles.buttonDisabled,
                ]}
                disabled={!selectedService}
                onPress={() => setShowServiceCard(true)}>
                <Icon
                  source={globalpath.plus}
                  size={wp(5)}
                  tintColor={selectedService ? colors.white : colors.greyBlack}
                />
                <ResponsiveText
                  style={[
                    styles.addServiceText,
                    !selectedService && styles.textDisabled,
                  ]}
                  color={selectedService ? colors.white : colors.greyBlack}
                  weight={'700'}>
                  Add Service
                </ResponsiveText>
              </TouchableOpacity>

              {savedServices.map((service, index) => (
                <SavedServiceCard
                  key={index}
                  service={service}
                  onEdit={() => handleEditService(index)}
                  onDelete={() => handleDeleteService(index)}
                />
              ))}

              {showServiceCard && (
                <ServiceCard
                  onBack={() => {
                    setShowServiceCard(false);
                    setSelectedService(null);
                  }}
                  service={selectedService}
                  onSave={handleServiceSave}
                  editData={savedServices.find(
                    s => s.reason.label === selectedService,
                  )}
                />
              )}
            </View>
          </CategoryCard>

          <DateTimePickerModal
            isVisible={isDatePickerVisible}
            mode="date"
            onConfirm={handleDateConfirm}
            onCancel={() => setDatePickerVisible(false)}
          />
        </ScrollView>
        <TouchableOpacity
          style={[
            styles.button,
            styles.saveButton,
            {backgroundColor: themeColor},
          ]}
          onPress={handleSave}
          disabled={loading}>
          {loading ? (
            <ActivityIndicator color="#fff" />
          ) : (
            <ResponsiveText style={styles.buttonText} color={colors.white}>
              Save
            </ResponsiveText>
          )}
        </TouchableOpacity>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor: colors.white,
  },
  content: {
    flex: 1,
    padding: wp(4),
    // paddingBottom:wp(20)
  },
  categoryContainer: {
    marginBottom: hp(2),
  },
  categoryCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: wp(4),
    backgroundColor: colors.lightGrey2,
    borderRadius: wp(2),
    borderWidth: 0.5,
    borderColor: colors.greyborder,
  },
  categoryCardActive: {
    // backgroundColor: themeColor,
  },
  categoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: wp(3),
  },
  categoryTitle: {
    fontSize: wp(4),
    // color: themeColor,
    fontWeight: '800',
  },
  categoryTitleActive: {
    color: colors.white,
  },
  expandedContent: {
    backgroundColor: colors.white,
    padding: wp(4),
    borderRadius: wp(2),
    marginTop: hp(1),
    borderWidth: 1,
    borderColor: colors.greyborder,
  },
  inputContainer: {
    marginBottom: hp(2),
  },
  inputLabel: {
    fontSize: wp(3.5),
    color: colors.darkGrey,
    marginBottom: hp(1),
  },
  input: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.greyborder,
    borderRadius: wp(1.5),
    paddingHorizontal: wp(3),
    // paddingVertical:wp(3)
  },
  input2: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.greyborder,
    borderRadius: wp(1.5),
    paddingHorizontal: wp(3),
    paddingVertical: wp(3),
    color: colors.black,
  },
  textInput: {
    flex: 1,
    padding: wp(3),
    fontSize: wp(3.5),
    color: colors.black,
  },
  addServiceButton: {
    flexDirection: 'row',
    alignItems: 'center',
    // backgroundColor: themeColor,
    padding: wp(3),
    borderRadius: wp(2),
    justifyContent: 'center',
    marginBottom: hp(2),
  },
  buttonDisabled: {
    backgroundColor: colors.greyborder,
  },
  addServiceText: {
    color: colors.white,
    marginLeft: wp(2),
    fontSize: wp(3.5),
    fontWeight: '600',
  },
  textDisabled: {
    color: colors.darkGrey,
  },
  serviceCardContainer: {
    backgroundColor: colors.white,
    padding: wp(4),
    borderRadius: wp(2),
    borderWidth: 1,
    borderColor: colors.greyborder,
    marginTop: hp(2),
  },
  paymentCard: {
    backgroundColor: colors.lightGrey2,
    padding: wp(4),
    borderRadius: wp(2),
    marginBottom: hp(2),
  },
  cardTitle: {
    fontSize: wp(4),
    fontWeight: '600',
    // color: themeColor,
    marginBottom: hp(2),
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: hp(2),
    justifyContent: 'space-between',
    marginTop: hp(1),
  },
  checkbox: {
    width: wp(6),
    height: wp(6),
    borderWidth: 2,
    // borderColor: themeColor,
    borderRadius: wp(1),
    marginRight: wp(2),
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxInner: {
    width: wp(3),
    height: wp(3),
    borderRadius: wp(0.5),
  },
  checkboxChecked: {
    // backgroundColor: themeColor,
  },
  checkboxLabel: {
    fontSize: wp(3.5),
    color: colors.black,
  },
  dateInput: {
    marginBottom: hp(2),
  },
  dateInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.greyborder,
    borderRadius: wp(2),
    paddingHorizontal: wp(3),
  },
  dateTextInput: {
    flex: 1,
    padding: wp(3),
    fontSize: wp(3.5),
    color: colors.black,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: hp(2),
  },
  button: {
    padding: wp(3),
    borderRadius: wp(2),
    alignItems: 'center',
    marginHorizontal: wp(4),
    marginBottom: hp(2),
  },
  backButton: {
    backgroundColor: colors.greyborder,
  },
  saveButton: {
    // backgroundColor: themeColor,
  },
  buttonText: {
    color: colors.white,
    fontSize: wp(3.5),
    fontWeight: '600',
  },
  addPaymentButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: wp(3),
    borderRadius: wp(2),
    justifyContent: 'center',
    marginBottom: hp(2),
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  addPaymentText: {
    color: colors.white,
    marginLeft: wp(2),
    fontSize: wp(3.5),
    fontWeight: '600',
  },
  savedPaymentCard: {
    padding: wp(4),
    borderWidth: 1,
    borderRadius: wp(3),
    marginBottom: hp(2),
    backgroundColor: colors.white,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  paymentFormCard: {
    padding: wp(4),
    borderRadius: wp(3),
    marginBottom: hp(2),
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.greyborder,
  },
  savedPaymentContent: {
    flexDirection: 'column',
  },
  paymentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: hp(2),
    borderBottomWidth: 1,
    borderBottomColor: colors.greyborder,
    paddingBottom: hp(1),
  },
  paymentDetails: {
    gap: hp(1),
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: hp(0.5),
  },
  savedPaymentInfo: {
    flex: 1,
  },
  savedPaymentActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: wp(2),
  },
  iconButton: {
    padding: wp(2),
    borderRadius: wp(2),
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 2,
  },
  savedServiceCard: {
    backgroundColor: colors.white,
    padding: wp(4),
    borderRadius: wp(2),
    marginVertical: hp(1),
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  savedServiceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: colors.greyborder,
    paddingBottom: hp(1),
    marginBottom: hp(2),
  },
  actionButtons: {
    flexDirection: 'row',
    gap: wp(2),
  },
  serviceDetails: {
    gap: hp(1),
  },
  paymentsSection: {
    marginTop: hp(2),
    backgroundColor: colors.lightGrey2,
    padding: wp(3),
    borderRadius: wp(2),
  },
  paymentItem: {
    backgroundColor: colors.white,
    padding: wp(2),
    borderRadius: wp(1),
    marginVertical: hp(0.5),
  },
  inputRequired: {
    borderColor: colors.red,
  },
  serviceCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: wp(4),
    borderTopLeftRadius: wp(2),
    borderTopRightRadius: wp(2),
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    // backgroundColor:"pink",
    flex: 1,
    marginRight: wp(8),
  },
  headerIconButton: {
    padding: wp(0.5),
  },
  serviceCardBody: {
    padding: wp(4),
  },
  infoSection: {
    gap: hp(2),
    paddingBottom: hp(2),
    borderBottomWidth: 1,
    borderBottomColor: colors.greyborder,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  paymentsContainer: {
    marginTop: hp(2),
  },
  paymentsSectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: hp(2),
  },
  paymentsList: {
    gap: hp(1),
  },
  paymentRecord: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: colors.lightGrey2,
    padding: wp(3),
    borderRadius: wp(2),
  },
  paymentAmount: {
    alignItems: 'flex-start',
  },
  adminSection: {
    marginBottom: hp(2),
  },
  phoneInputContainer: {
    width: '100%',
    // height: "20%",
    height: 'auto',
    backgroundColor: colors.lightGrey2,
    borderRadius: wp(3),
    borderWidth: 1,
    borderColor: colors.greyborder,
    marginBottom: hp(1),
  },
  phoneInputTextContainer: {
    backgroundColor: colors.lightGrey2,
    borderRadius: wp(3),
  },
  phoneInputText: {
    fontSize: wp(4),
    color: colors.greyBlack,
    // backgroundColor:"pink"
  },
  phoneInputCodeText: {
    fontSize: wp(4),
    color: colors.greyBlack,
  },
  phoneInputFlagButton: {
    width: wp(15),
  },
  inputLabel: {
    color: colors.red,
  },
});

export default Add_Customer;
