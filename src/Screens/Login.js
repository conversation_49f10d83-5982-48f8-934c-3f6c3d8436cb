import {
  SafeAreaView,
  StyleSheet,
  View,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from 'react-native';
import React, {useState} from 'react';
import {colors} from '../Custom/Colors';
import Icon from '../Custom/Icon';
import {globalpath} from '../Custom/globalpath';
import {wp, hp} from '../Custom/Responsiveness';
import ResponsiveText from '../Custom/RnText';
import {AuthAPI} from '../api/services/auth';
import AsyncStorage from '@react-native-async-storage/async-storage';
import useAppText from '../hooks/useAppText';
import LanguageSwitch from '../Components/LanguageSwitch';

const Login = ({navigation}) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const AppText = useAppText();
  const handleLogin = async () => {
    try {
      setLoading(true);

      // 1. Login API call
      const response = await AuthAPI.login(email, password);
      console.log('Login success:', response.data);
      // 2. Save token & user data to AsyncStorage
      await AsyncStorage.setItem('token', response.data.token.access);
      await AsyncStorage.setItem('user', JSON.stringify(response.data));

      // 3. Get FCM token from AsyncStorage
      const fcmToken = await AsyncStorage.getItem('FCMToken');
      const adminId = response?.data?.user?.id || response?.data?.id; // adjust based on your API response
      console.log('Admin ID:', adminId);
      console.log('FCM Token:', fcmToken);
      // 4. Send FCM token to backend
      if (fcmToken && adminId) {
        const response = await AuthAPI.StoreFCM({
          fcmToken: fcmToken,
          adminId: adminId,
        });
        console.log('FCM token stored successfully:', response);
      } else {
        console.warn('Missing FCM token or admin ID');
      }

      setLoading(false);
      navigation.replace('AppStack');
    } catch (error) {
      setLoading(false);
      console.log('Login failed:', error?.response?.data);

      if (error?.response?.data?.error) {
        Alert.alert(error?.response?.data?.error?.message);
      } else {
        Alert.alert('Error', error?.message || 'Something went wrong');
      }
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <TouchableOpacity onPress={() => navigation.goBack()}>
        <Icon
          source={globalpath.back}
          size={wp(6)}
          margin={[wp(3), 0, 0, wp(4)]}
        />
      </TouchableOpacity>
      <View style={styles.headerContainer}>
        {/* <Icon
          source={globalpath.admins}
          size={wp(30)}
          margin={[hp(2), 0]}
        /> */}
        <ResponsiveText style={styles.title} size={7}>
          {AppText.WELCOME_BACK}
        </ResponsiveText>
        <ResponsiveText style={styles.subtitle} size={3.5}>
          {AppText.SIGN_IN_TO_CONTINUE}
        </ResponsiveText>
      </View>

      <View style={styles.formContainer}>
        <LanguageSwitch />
        <View style={styles.inputContainer}>
          <Icon
            source={globalpath.mail}
            size={wp(5)}
            margin={[0, wp(3), 0, 0]}
            tintColor={colors.darkGrey}
          />
          <TextInput
            style={styles.input}
            placeholder={AppText.EMAIL_PLACEHOLDER}
            value={email}
            onChangeText={setEmail}
            keyboardType="email-address"
            autoCapitalize="none"
            placeholderTextColor={colors.darkGrey}
          />
        </View>

        <View style={styles.inputContainer}>
          <Icon
            source={globalpath.lock}
            size={wp(5)}
            margin={[0, wp(3), 0, 0]}
            tintColor={colors.darkGrey}
          />
          <TextInput
            style={styles.input}
            placeholder={AppText.PASSWORD_PLACEHOLDER}
            value={password}
            onChangeText={setPassword}
            secureTextEntry={!showPassword}
            placeholderTextColor={colors.darkGrey}
          />
          <TouchableOpacity onPress={() => setShowPassword(!showPassword)}>
            <Icon
              source={showPassword ? globalpath.eyeOff : globalpath.eye}
              size={wp(5)}
              tintColor={colors.darkGrey}
            />
          </TouchableOpacity>
        </View>

        <TouchableOpacity
          onPress={() => navigation.navigate('ForgotPassword')}
          style={styles.forgotPasswordContainer}>
          <ResponsiveText style={styles.forgotPasswordText} size={4}>
            {AppText.FORGOT_PASSWORD}
          </ResponsiveText>
        </TouchableOpacity>
        {loading ? (
          <View style={styles.loginButton}>
            <ActivityIndicator size="small" color={colors.white} />
          </View>
        ) : (
          <TouchableOpacity
            style={styles.loginButton}
            // onPress={() => navigation.navigate('DrawerNavigator')}
            onPress={() => handleLogin()}>
            <ResponsiveText color={colors.white} size={4} weight="bold">
              {AppText.LOGIN_BUTTON}
            </ResponsiveText>
          </TouchableOpacity>
        )}
      </View>
    </SafeAreaView>
  );
};

export default Login;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    // padding: wp(5),
  },
  headerContainer: {
    alignItems: 'center',
    // backgroundColor:"pink",
    marginVertical: wp(15),
  },
  title: {
    color: colors.Light_theme_purple,
    fontWeight: 'bold',
    marginBottom: hp(1),
  },
  subtitle: {
    color: colors.darkGrey,
  },
  formContainer: {
    width: '93%',
    alignSelf: 'center',
    // paddingVertical: hp(6),
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.lightGrey2,
    borderRadius: wp(3),
    marginBottom: hp(2),
    paddingHorizontal: wp(4),
    height: hp(7),
    borderWidth: 1,
    borderColor: colors.greyborder,
  },
  input: {
    flex: 1,
    fontSize: wp(4),
    color: colors.greyBlack,
  },
  forgotPasswordContainer: {
    alignItems: 'flex-end',
    marginBottom: hp(4),
  },
  forgotPasswordText: {
    color: colors.Light_theme_purple,
    fontWeight: '600',
  },
  loginButton: {
    backgroundColor: colors.Light_theme_purple,
    height: hp(7),
    borderRadius: wp(3),
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: colors.Light_theme_purple,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
  },
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: hp(3),
    paddingHorizontal: wp(5),
  },
  divider: {
    flex: 1,
    height: 1,
    backgroundColor: colors.greyborder,
  },
  dividerText: {
    marginHorizontal: wp(3),
    color: colors.darkGrey,
  },
  phoneLoginButton: {
    flexDirection: 'row',
    // backgroundColor: colors.Light_theme_purple,
    height: hp(7),
    borderRadius: wp(3),
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: wp(5),
  },
});
