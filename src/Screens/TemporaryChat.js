import React, {useCallback, useState} from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  Image,
  ScrollView,
} from 'react-native';
import {colors} from '../Custom/Colors';
import ResponsiveText from '../Custom/RnText';
import CustomHeader from '../Components/CustomHeader';
import AsyncStorage from '@react-native-async-storage/async-storage';
import socket from '../Sockets/socketService';
import Icon from '../Custom/Icon';
import {globalpath} from '../Custom/globalpath';
import {hp, wp} from '../Custom/Responsiveness';
import {useFocusEffect} from '@react-navigation/native';
import LanguageModal from '../Components/LanguageModal';
const getLabel = code => {
  const item = {
    al: 'Albanian',
    bn: 'Bengali',
    ea: 'Egyption',
    en: 'English',
    es: 'Spanish',
    fr: 'French',
    hi: 'Hindi',
    it: 'Italian',
    ma: 'Moroccan',
    sn: 'Shona',
    ta: 'Arabic',
    ur: 'Urdu',
    yo: 'Yoruba',
    zh: 'Chinese',
  };
  return item[code] || code;
};
const TemporaryChat = ({navigation}) => {
  const [user, setUser] = useState(null);
  const [practiceNumber, setpracticeNumber ] = useState(null);
  const [userDetails, setUserDetails] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [language, setLanguage] = useState('it');

  const JoinChatRoom = (chat, idtype) => {
    const conversationId = `${idtype}_${chat.phoneNumber}`;
    socket.emit('joinChat', {chat, userDetails, conversationId, idtype});
  };


  useFocusEffect(
    useCallback(() => {
      const fetchUserData = async () => {
        try {
          const userData = await AsyncStorage.getItem('user');
          if (userData) {
            const parsedUser = JSON.parse(userData);
            console.log('parsed user', parsedUser);
            console.log('parsed user', parsedUser.user.practiceNumber);
            setpracticeNumber(parsedUser.user.practiceNumber);
            setUser(parsedUser);
            setUserDetails({
              active: null,
              avatar: null,
              email: null,
              id: parsedUser.user.id,
              name: parsedUser.user.firstName,
              role: 'client',
              phoneNumber: parsedUser.user.phoneNumber,
            });
          }
        } catch (error) {
          console.error('Error fetching user data:', error);
        }
      };

      fetchUserData();
    }, []),
  );

  return (
    <SafeAreaView style={styles.safeArea}>
      <CustomHeader title="My Profile" />

      <ScrollView contentContainerStyle={styles.container}>
        {/* Integrated Header Section */}
        <View style={styles.headerSection}>
          <View style={styles.logoContainer}>
            <Image
              source={globalpath.logo}
              style={styles.integatedLogo}
              resizeMode="contain"
            />
          </View>
          <View style={styles.headerDecoration} />
        </View>

        {/* Card-Based Layout */}
        {user && (
          <View style={styles.mainContent}>
            {/* Enhanced User Identity Card */}
            <View style={styles.identityCard}>
              <View style={styles.avatarSection}>
                <View style={styles.avatarContainer}>
                  <View style={styles.avatarRing}>
                    <View style={styles.modernAvatar}>
                      <ResponsiveText color={colors.white} size={5.5} weight={'700'}>
                        {user?.user?.firstName?.charAt(0)?.toUpperCase() || ''}
                      </ResponsiveText>
                    </View>
                  </View>
                  {/* <View style={styles.statusDot} /> */}
                </View>
                <View style={styles.userInfo}>
                  <ResponsiveText size={5.2} weight="bold" style={styles.userName} numberOfLines={1} maxWidth={wp(40)}>
                    {user?.user?.firstName} {user?.user?.lastName} 
                  </ResponsiveText>
                  <View style={styles.badgeContainer}>
                    <View style={styles.activeBadge}>
                      {/* <View style={styles.badgeIcon} /> */}
                      <ResponsiveText size={3} style={styles.badgeText}>
                        {practiceNumber}
                      </ResponsiveText>
                    </View>
                  </View>
                </View>
              </View>

              <TouchableOpacity
                style={styles.editAction}
                onPress={() => navigation.navigate('Profile', {user: user})}>
                <View style={styles.editButton}>
                  <Icon
                    source={globalpath.edit}
                    size={wp(4.5)}
                    tintColor={colors.white}
                  />
                </View>
              </TouchableOpacity>
            </View>

            {/* Information Grid */}
            <View style={styles.infoGrid}>
              {/* Phone Card */}
              <View style={styles.infoCard}>
                <View style={styles.cardHeader}>
                  <View style={styles.iconBadge}>
                    <Icon
                      source={globalpath.phonecall}
                      size={wp(4.5)}
                      tintColor={colors.Light_theme_purple}
                    />
                  </View>
                  <ResponsiveText size={3.5} style={styles.cardTitle}>
                    Contact
                  </ResponsiveText>
                </View>
                <ResponsiveText size={4.5} style={styles.cardValue}>
                  {user?.user?.phoneNumber}
                </ResponsiveText>
              </View>

              {/* Language Card */}
              <View style={styles.infoCard}>
                <View style={[styles.cardHeader,{justifyContent:"space-between"}]}>
                  <View style={{alignItems:'center',flexDirection:'row',}}>
                      <View style={styles.iconBadge}>
                    <Icon
                      source={globalpath.language}
                      size={wp(4.5)}
                      tintColor={colors.Light_theme_purple}
                    />
                  </View>
                  <ResponsiveText size={3.5} style={styles.cardTitle}>
                    Language
                  </ResponsiveText>
                  </View>
                
                  <View style={{alignSelf:"flex-end"}}>
                                <TouchableOpacity
                  onPress={() => setShowModal(true)}
                  style={styles.languageChip}>
                  <ResponsiveText size={4} style={styles.chipText}>
                    {getLabel(language)}
                  </ResponsiveText>
                  <Icon
                    source={globalpath.down || globalpath.back}
                    size={wp(3)}
                    tintColor={colors.greyBlack}
                    style={styles.chipIcon}
                  />
                </TouchableOpacity>
                  </View>
       
                </View>
               
              </View>
            </View>

            <LanguageModal
              visible={showModal}
              onClose={() => setShowModal(false)}
              onSelect={val => setLanguage(val)}
              selectedValue={language}
            />
          </View>
        )}
      </ScrollView>

      {/* Geometric Chat Button */}
      <TouchableOpacity
        style={styles.chatFab}
        onPress={() => {
          navigation.navigate('ChatScreen', {
            item: user.user,
            isFromCustomer: false,
            group: 'client',
            lang: language,
            tempChat: true,
          });
          JoinChatRoom(user.user, 'client');
        }}>
        <View style={styles.fabBackground} />
        <Icon source={globalpath.convo} size={wp(6.5)} tintColor={colors.white} />
      </TouchableOpacity>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: colors.lightGrey2,
  },
  container: {
    paddingHorizontal: wp(4),
    paddingBottom: hp(12),
  },
  headerSection: {
    backgroundColor: colors.white,
    marginHorizontal: -wp(4),
    marginTop: -hp(1),
    paddingHorizontal: wp(6),
    paddingVertical: hp(3),
    borderBottomLeftRadius: wp(8),
    borderBottomRightRadius: wp(8),
    marginBottom: hp(3),
    // shadowColor: colors.black,
    // shadowOffset: {width: 0, height: 4},
    // shadowOpacity: 0.06,
    // shadowRadius: 12,
    // elevation: 6,
    position: 'relative',
  },
  logoContainer: {
    alignItems: 'center',
    paddingVertical: hp(2),
  },
  integatedLogo: {
    width: wp(30),
    height: wp(30),
    opacity: 0.9,
  },
  headerDecoration: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: hp(0.1),
    // backgroundColor: `${colors.Light_theme_purple}15`,
  },
  mainContent: {
    flex: 1,
  },
  identityCard: {
    backgroundColor: colors.white,
    borderRadius: wp(5),
    padding: wp(6),
    marginBottom: hp(3),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    // shadowColor: colors.Light_theme_purple,
    // shadowOffset: {width: 0, height: 6},
    // shadowOpacity: 0.12,
    // shadowRadius: 16,
    // elevation: 8,
    borderWidth: 1,
    borderColor: `${colors.Light_theme_purple}08`,
  },
  avatarSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatarContainer: {
    position: 'relative',
    marginRight: wp(4),
  },
  avatarRing: {
    width: wp(18),
    height: wp(18),
    borderRadius: wp(9),
    backgroundColor: `${colors.Light_theme_purple}15`,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: `${colors.Light_theme_purple}30`,
  },
  modernAvatar: {
    width: wp(14),
    height: wp(14),
    borderRadius: wp(7),
    backgroundColor: colors.Light_theme_purple,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: colors.Light_theme_purple,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 2,
  },
  statusDot: {
    position: 'absolute',
    bottom: wp(1),
    right: wp(1),
    width: wp(4),
    height: wp(4),
    borderRadius: wp(2),
    backgroundColor: colors.lightgreen,
    borderWidth: 2,
    borderColor: colors.white,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    color: colors.greyBlack,
    marginBottom: hp(0.8),
  },
  badgeContainer: {
    flexDirection: 'row',
  },
  activeBadge: {
    backgroundColor: colors.lightGrey1,
    paddingHorizontal: wp(3),
    paddingVertical: hp(0.5),
    borderRadius: wp(3),
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: colors.lightgreen,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  badgeIcon: {
    width: wp(2),
    height: wp(2),
    borderRadius: wp(1),
    backgroundColor: colors.white,
    marginRight: wp(1.5),
  },
  badgeText: {
    color: colors.white,
    fontWeight: '700',
    fontSize: wp(2.8),
  },
  editAction: {
    padding: wp(1),
  },
  editButton: {
    width: wp(10),
    height: wp(10),
    borderRadius: wp(5),
    backgroundColor: colors.Light_theme_purple,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: colors.Light_theme_purple,
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  infoGrid: {
    gap: hp(2),
  },
  infoCard: {
    backgroundColor: colors.white,
    borderRadius: wp(3),
    padding: wp(4),
    shadowColor: colors.black,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: hp(1.5),
  },
  iconBadge: {
    width: wp(8),
    height: wp(8),
    borderRadius: wp(2),
    backgroundColor: `${colors.Light_theme_purple}15`,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: wp(3),
  },
  cardTitle: {
    color: colors.lightGrey5,
    fontWeight: '500',
  },
  cardValue: {
    color: colors.greyBlack,
    fontWeight: '600',
    marginLeft: wp(11),
  },
  languageChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.lightGrey6,
    paddingHorizontal: wp(3),
    paddingVertical: hp(0.8),
    borderRadius: wp(5),
    marginLeft: wp(11),
    alignSelf: 'flex-start',
  },
  chipText: {
    color: colors.greyBlack,
    fontWeight: '500',
    marginRight: wp(1),
  },
  chipIcon: {
    transform: [{rotate: '90deg'}],
  },
  chatFab: {
    position: 'absolute',
    bottom: hp(4),
    right: wp(6),
    width: wp(16),
    height: wp(16),
    borderRadius: wp(8),
    backgroundColor: colors.Light_theme_purple,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: colors.Light_theme_purple,
    shadowOffset: {width: 0, height: 6},
    shadowOpacity: 0.3,
    shadowRadius: 10,
    elevation: 8,
    overflow: 'hidden',
  },
  fabBackground: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    backgroundColor: colors.Light_theme_purple,
    borderRadius: wp(8),
  },
});

export default TemporaryChat;
