import React, {useEffect, useState, useRef} from 'react';
import {
  StyleSheet,
  View,
  Animated,
  Keyboard,
  TouchableOpacity,
} from 'react-native';
import DropDownPicker from 'react-native-dropdown-picker';
import {colors} from '../Custom/Colors';
import {hp, wp} from '../Custom/Responsiveness';
import Icon from '../Custom/Icon';
import {globalpath} from '../Custom/globalpath';
import {OfficeServices} from '../api/services/officeServices';
import AsyncStorage from '@react-native-async-storage/async-storage';

const AddOfficeDropDown = ({value, setValue, height = 4.9, istrue = false}) => {
  const [open, setOpen] = useState(false);
  const [rotateAnimation] = useState(new Animated.Value(0));
  const [adminData, setAdminData] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [initialLoaded, setInitialLoaded] = useState(false);
  const [shouldScrollToSelected, setShouldScrollToSelected] = useState(true); // New state to track scrolling
  const flatListRef = useRef(null); // Reference for FlatList
  const [user, setUser] = useState(null);
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const [dropdownReady, setDropdownReady] = useState(true);


  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const userData = await AsyncStorage.getItem('user');
        if (userData) {
          const parsedUser = JSON.parse(userData);
          console.log('User data:', parsedUser);
          setUser(parsedUser);
          fetchData(1, '', parsedUser?.id);
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
      }
    };

    fetchUserData();
  }, []);

  // Add keyboard listeners to track keyboard state
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
      setKeyboardVisible(true);
    });
    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
      setKeyboardVisible(false);
      setDropdownReady(true);
    });

    return () => {
      keyboardDidShowListener?.remove();
      keyboardDidHideListener?.remove();
    };
  }, []);
  const rotateIcon = () => {
    // fetchData();
    Animated.timing(rotateAnimation, {
      toValue: open ? 1 : 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const spin = rotateAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: ['180deg', '0deg'], // Changed: start with up arrow (180deg), rotate to down (0deg) when open
  });

  // useEffect(() => {
  //   fetchData(currentPage);
  // }, []);

  useEffect(() => {
    if (open && value && shouldScrollToSelected) {
      // Scroll to the selected item when dropdown is focused
      const index = adminData.findIndex(item => item.value === value?.id);
      if (index !== -1 && flatListRef.current) {
        flatListRef.current.scrollToIndex({index, animated: true});
      }
    }
  }, [open, value, adminData, shouldScrollToSelected]);

  const fetchData = async (page = 1, search = '', adminId = user?.id) => {
    console.warn("userID---,",adminId);
    if (istrue) {
      if (isLoading || (!search && page <= currentPage && initialLoaded))
        return;
    }
    console.warn;
    setIsLoading(true);
    try {
      const response = await OfficeServices.getOffice({
        page,
        search,
        adminId,
      });
      setCurrentPage(page);
      setHasMore(response.data.totalPages > page);
      setInitialLoaded(true);
      // Transform data for react-native-dropdown-picker format
      const transformedData = response.data.offices.map(office => ({
        label: office.name,
        value: office.id,
        ...office // Keep original data for setValue
      }));
      console.log('Transformed data:', transformedData);
      setAdminData(prev =>
        page === 1 || search
          ? transformedData
          : [...prev, ...transformedData],
      );
    } catch (error) {
      console.error('Fetch error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const clearSelection = () => {
    setValue(null);
    setOpen(false);
    rotateIcon();
  };



  console.log('AdminData for dropdown:', adminData);
  console.log('DropdownReady:', dropdownReady);
  console.log('Current value:', value);

  return (
    <View style={styles.container}>
      <DropDownPicker
        open={open}
        value={value?.id || null}
        items={dropdownReady ? adminData : []}
        setOpen={(openState) => {
          if (keyboardVisible && openState) {
            // If keyboard is visible, prevent dropdown from opening immediately
            setDropdownReady(false);
            setOpen(false); // Ensure dropdown doesn't show while keyboard is dismissing
            Keyboard.dismiss();
            // Wait for keyboard to fully dismiss before opening dropdown
            setTimeout(() => {
              setDropdownReady(true);
              setOpen(true);
              rotateIcon();
              setShouldScrollToSelected(true);
            }, 350); // Slightly longer delay for smoother transition
          } else {
            // If keyboard is not visible, open dropdown immediately
            setOpen(openState);
            rotateIcon();
            setShouldScrollToSelected(openState);
          }
        }}
        setValue={(callback) => {
          const selectedValue = callback(value?.id || null);
          console.log('Selected value:', selectedValue);
          // Find the full item data from adminData
          const selectedItem = adminData.find(item => item.value === selectedValue);
          if (selectedItem) {
            setValue(selectedItem);
          }
          setOpen(false);
          rotateIcon();
        }}
        setItems={setAdminData}
        placeholder={!dropdownReady && keyboardVisible ? "Loading..." : "Select Office"}
        searchable={false}
        loading={isLoading}
        disabled={!dropdownReady && keyboardVisible}
        style={[
          styles.dropdown,
          {height: hp(height)},
          open && styles.dropdownFocus,
          (!dropdownReady && keyboardVisible) && styles.dropdownDisabled,
        ]}
        dropDownContainerStyle={styles.dropdownContainer}
        textStyle={styles.selectedTextStyle}
        placeholderStyle={styles.placeholderStyle}
        listItemLabelStyle={styles.dropdownItemText}
        selectedItemLabelStyle={styles.selectedItemLabelStyle}
        maxHeight={hp(25)}
        zIndex={1000}
        zIndexInverse={3000}
        ArrowDownIconComponent={() => {
          if (value) {
            // Show cross button when item is selected
            return (
              <TouchableOpacity onPress={clearSelection}>
                <Icon source={globalpath.cross} size={wp(4)} tintColor={colors.grey} />
              </TouchableOpacity>
            );
          }
          // Show rotating arrow when no item selected
          return (
            <Animated.View style={{transform: [{rotate: spin}]}}>
              <Icon source={globalpath.up} size={wp(3)} tintColor={colors.grey} />
            </Animated.View>
          );
        }}
        ArrowUpIconComponent={() => {
          if (value) {
            // Show cross button when item is selected
            return (
              <TouchableOpacity onPress={clearSelection}>
                <Icon source={globalpath.cross} size={wp(4)} tintColor={colors.grey} />
              </TouchableOpacity>
            );
          }
          // Show rotating arrow when no item selected
          return (
            <Animated.View style={{transform: [{rotate: spin}]}}>
              <Icon source={globalpath.up} size={wp(3)} tintColor={colors.grey} />
            </Animated.View>
          );
        }}
        flatListProps={{
          ref: flatListRef,
          onEndReached: () => {
            if (hasMore && !isLoading) {
              fetchData(currentPage + 1, '', user?.id);
            }
          },
          onEndReachedThreshold: 0.5,
          getItemLayout: (_, index) => ({
            length: hp(5), // Adjust this based on your item height
            offset: hp(5) * index,
            index,
          }),
        }}
      />
    </View>
  );
};

export default AddOfficeDropDown;

const styles = StyleSheet.create({
  container: {
    width: '99%',
    position: 'relative', // Needed to position the dropdown correctly
    zIndex: 1, // Ensure proper layering
  },
  dropdown: {
    backgroundColor: colors.white,
    borderRadius: wp(2),
    paddingHorizontal: wp(4),
    borderWidth: 1,
    borderColor: colors.greyborder,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
  },
  dropdownFocus: {
    borderColor: colors.Light_theme_purple,
    // borderWidth: 1.5,
    // shadowOpacity: 0.1,
  },
  dropdownDisabled: {
    opacity: 0.6,
    backgroundColor: colors.lightGrey2,
  },
  dropdownContainer: {
    borderRadius: wp(2),
    borderWidth: 1,
    borderColor: colors.greyborder,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 3,
    backgroundColor: colors.white,
  },
  selectedTextStyle: {
    fontSize: wp(3.8),
    color: colors.black,
    fontWeight: '500',
  },
  placeholderStyle: {
    fontSize: wp(3.8),
    color: colors.darkGrey,
  },

  dropdownItemText: {
    fontSize: wp(3.8),
    color: colors.black,
    fontWeight: '500',
  },
  selectedItemLabelStyle: {
    fontSize: wp(3.8),
    color: colors.Light_theme_purple,
    fontWeight: '600',
  },
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: wp(3),
    borderBottomWidth: 1,
    borderBottomColor: colors.lightGrey3,
  },
  itemTextContainer: {
    flex: 1,
  },
  itemName: {
    fontSize: wp(3.8),
    color: colors.black,
    fontWeight: '500',
  },
  itemEmail: {
    fontSize: wp(3.2),
    color: colors.darkGrey,
    marginTop: hp(0.5),
  },
  iconsContainer: {
    position: 'absolute',
    top: wp(2),
    right: wp(2),
    flexDirection: 'row',
    alignItems: 'center',
    zIndex: 1,
  },
  clearButton: {
    marginHorizontal: wp(4),
  },
});
