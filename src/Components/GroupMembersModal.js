import React, { useRef, useEffect } from 'react';
import {
  Modal,
  View,
  TouchableOpacity,
  StyleSheet,
  Platform,
  Animated,
  ScrollView,
  Dimensions,
} from 'react-native';
import { colors } from '../Custom/Colors';
import { hp, wp } from '../Custom/Responsiveness';
import ResponsiveText from '../Custom/RnText';
import Icon from '../Custom/Icon';
import { globalpath } from '../Custom/globalpath';
import useTheme from '../Redux/useTheme';
import * as Animatable from 'react-native-animatable';

const { width, height } = Dimensions.get('window');

const GroupMembersModal = ({ visible, onClose }) => {
  const { themeColor, backgroundColor, getTextColor, getSecondaryTextColor } = useTheme();
  const slideAnim = useRef(new Animated.Value(hp(70))).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;

  // Static group members data - simple name only
  const groupMembers = [
    {
      id: 1,
      name: '<PERSON><PERSON>',
    },
    {
      id: 2,
      name: '<PERSON>',
    },
    {
      id: 3,
      name: '<PERSON><PERSON>',
    },
    {
      id: 4,
      name: '<PERSON>',
    },
    {
      id: 5,
      name: '<PERSON> Raza',
    },
  ];

  // Function to get initials from name
  const getInitials = (name) => {
    return name.split(' ').map(word => word.charAt(0)).join('').toUpperCase();
  };

  // Function to generate unique avatar color based on name
  const generateAvatarColor = (name) => {
    // Create a hash from the name
    let hash = 0;
    for (let i = 0; i < name.length; i++) {
      hash = name.charCodeAt(i) + ((hash << 5) - hash);
    }

    // Convert hash to HSL color for better color distribution
    const hue = Math.abs(hash) % 360;
    const saturation = 65 + (Math.abs(hash) % 20); // 65-85%
    const lightness = 45 + (Math.abs(hash) % 15); // 45-60%

    return `hsl(${hue}, ${saturation}%, ${lightness}%)`;
  };

  useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.spring(slideAnim, {
          toValue: 0,
          useNativeDriver: true,
          tension: 50,
          friction: 8,
        }),
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: hp(70),
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 250,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible]);

  const renderMemberCard = (member, index) => (
    <Animatable.View
      key={member.id}
      animation="fadeInRight"
      delay={index * 100}
      duration={600}
      style={styles.memberCard}
    >
      {/* Avatar */}
      <View style={[styles.avatar, { backgroundColor: generateAvatarColor(member.name) }]}>
        <ResponsiveText color={colors.white} size={4.2} weight="700">
          {getInitials(member.name)}
        </ResponsiveText>
      </View>

      {/* Name */}
      <ResponsiveText
        color={getTextColor()}
        size={4.2}
        weight="600"
        numberOfLines={1}
        style={styles.memberName}
      >
        {member.name}
      </ResponsiveText>
    </Animatable.View>
  );

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      statusBarTranslucent
      onRequestClose={onClose}
    >
      <Animated.View
        style={[
          styles.modalBackdrop,
          {
            opacity: fadeAnim,
          },
        ]}
      >
        <TouchableOpacity
          style={styles.touchableOutside}
          activeOpacity={1}
          onPress={onClose}
        />

        <Animated.View
          style={[
            styles.modalContainer,
            { backgroundColor },
            {
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          {/* Handle bar */}
          <View style={styles.handleBar} />

          {/* Header */}
          <View style={styles.modalHeader}>
            <ResponsiveText color={getTextColor()} weight="700" size={5}>
              Group Members
            </ResponsiveText>

            <TouchableOpacity
              onPress={onClose}
              style={[styles.closeButton, { backgroundColor: colors.lightGrey6 }]}
              activeOpacity={0.7}
            >
              <Icon
                source={globalpath.cross}
                size={wp(4.5)}
                tintColor={colors.greyBlack}
              />
            </TouchableOpacity>
          </View>

          {/* Members list */}
          <ScrollView
            style={styles.membersContainer}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.membersContent}
          >
            {groupMembers.map((member, index) => renderMemberCard(member, index))}
          </ScrollView>
        </Animated.View>
      </Animated.View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalBackdrop: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.6)',
    justifyContent: 'flex-end',
  },
  touchableOutside: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  modalContainer: {
    width: '100%',
    height: hp(70),
    borderTopLeftRadius: wp(6),
    borderTopRightRadius: wp(6),
    paddingTop: hp(3),
    paddingHorizontal: wp(5),
    paddingBottom: Platform.select({
      ios: hp(4),
      android: hp(3),
    }),
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -10,
    },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 15,
  },
  handleBar: {
    width: wp(12),
    height: hp(0.6),
    backgroundColor: colors.lightGrey5,
    borderRadius: wp(3),
    alignSelf: 'center',
    marginBottom: hp(2),
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: hp(3),
    paddingHorizontal: wp(1),
  },
  closeButton: {
    padding: wp(2.5),
    borderRadius: wp(6),
  },
  membersContainer: {
    flex: 1,
  },
  membersContent: {
    paddingBottom: hp(2),
  },
  memberCard: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: hp(2),
    paddingHorizontal: wp(4),
    marginBottom: hp(1.5),
    borderRadius: wp(4),
    backgroundColor: 'rgba(0,0,0,0.02)',
  },
  avatar: {
    width: wp(12),
    height: wp(12),
    borderRadius: wp(6),
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: wp(4),
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  memberName: {
    flex: 1,
  },
});

export default GroupMembersModal;
