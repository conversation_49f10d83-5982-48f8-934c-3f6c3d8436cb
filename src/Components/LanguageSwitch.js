import React from 'react';
import { View, StyleSheet, Switch } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { setLanguage } from '../Redux/Slices/themeSlice';
import ResponsiveText from '../Custom/RnText';
import { colors } from '../Custom/Colors';
import { wp, hp } from '../Custom/Responsiveness';
import useAppText from '../hooks/useAppText';

const LanguageSwitch = () => {
  const dispatch = useDispatch();
  const currentLanguage = useSelector((state) => state.theme.language);
  const AppText = useAppText();

  const handleLanguageSwitchChange = () => {
    const newLanguage = currentLanguage === 'en' ? 'it' : 'en';
    dispatch(setLanguage(newLanguage));
  };

  return (
    <View style={styles.container}>
      <ResponsiveText style={styles.languageText} size={4}>
        {currentLanguage === 'en' ? AppText.ENGLISH : AppText.ITALIAN}
      </ResponsiveText>
      <Switch
        value={currentLanguage === 'it'}
        onValueChange={handleLanguageSwitchChange}
        trackColor={{ false: colors.lightGrey2, true: colors.Light_theme_purple }}
        thumbColor={currentLanguage === 'it' ? colors.white : colors.darkGrey}
        ios_backgroundColor={colors.lightGrey2}
        style={styles.switch}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: wp(4),
    paddingVertical: hp(1),
    backgroundColor: colors.lightGrey2,
    borderRadius: wp(3),
    marginBottom: hp(2),
    borderWidth: 1,
    borderColor: colors.greyborder,
  },
  languageText: {
    color: colors.greyBlack,
    fontWeight: '600',
  },
  switch: {
    transform: [{ scaleX: 0.9 }, { scaleY: 0.9 }],
  },
});

export default LanguageSwitch;
